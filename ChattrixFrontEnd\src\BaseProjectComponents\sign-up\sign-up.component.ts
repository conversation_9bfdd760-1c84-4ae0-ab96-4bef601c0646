import { Component, OnInit, OnDestroy } from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  Validators,
  AbstractControl,
} from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { AuthenticationService } from '../../app/Pages/authentication/Services/Authentication.service';
import { AuthStateService } from '../../app/Pages/authentication/Services/AuthState.service';
import { NotificationService } from '../../src/app/Core/Services/notification.service';
import {
  RegisterRequest,
  AuthState,
  AuthError,
  UserInfo,
} from '../../app/Pages/authentication/Models';

@Component({
  selector: 'app-sign-up',
  standalone: false,
  templateUrl: './sign-up.component.html',
  styleUrl: './sign-up.component.scss',
})
export class SignUpComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  signupForm: FormGroup;
  hidePassword = true;
  hideConfirmPassword = true;
  logoLoaded = true; // Assume logo loads successfully by default

  // Authentication state
  isAuthenticated = false;
  isLoading = false;
  user: UserInfo | null = null;
  error: string | null = null;

  constructor(
    private authService: AuthenticationService,
    private authState: AuthStateService,
    private formBuilder: FormBuilder,
    private router: Router,
    private notificationService: NotificationService,
  ) {
    this.signupForm = this.formBuilder.group(
      {
        fullName: ['', [Validators.required, Validators.minLength(2)]],
        email: ['', [Validators.required, Validators.email]],
        password: [
          '',
          [
            Validators.required,
            Validators.minLength(8),
            this.passwordValidator,
          ],
        ],
        confirmPassword: ['', [Validators.required]],
        phoneNumber: [
          '',
          [
            Validators.pattern(/^\+?[\d\s\-\(\)]+$/),
            Validators.maxLength(15),
            Validators.minLength(10),
            Validators.required,
          ],
        ],
      },
      { validators: this.passwordMatchValidator },
    );
  }

  ngOnInit(): void {
    // Subscribe to authentication state
    this.authState.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: AuthState) => {
        this.isAuthenticated = state.isAuthenticated;
        this.isLoading = state.isLoading;
        this.user = state.user;
        this.error = state.error;

        // Show error messages
        if (state.error) {
          this.showError(state.error);
        }

        // Redirect if authenticated
        if (state.isAuthenticated && state.user) {
          this.router.navigate(['/dashboard']);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onSignUp(): void {
    if (this.signupForm.valid) {
      // Store signup data in session storage for profile completion
      const signupData = {
        fullName: this.signupForm.value.fullName,
        email: this.signupForm.value.email,
        password: this.signupForm.value.password,
        phoneNumber: this.signupForm.value.phoneNumber || undefined,
      };

      // Store the signup data temporarily for the profile picture step
      sessionStorage.setItem('pendingSignupData', JSON.stringify(signupData));

      this.showSuccess(
        'Basic information saved! Please complete your profile.',
      );

      // Navigate to profile picture component
      this.router.navigate(['/auth/profile-picture']);
    } else {
      this.markFormGroupTouched();
    }
  }

  onLogin(): void {
    this.router.navigate(['/auth/login']);
  }

  togglePasswordVisibility(): void {
    this.hidePassword = !this.hidePassword;
  }

  toggleConfirmPasswordVisibility(): void {
    this.hideConfirmPassword = !this.hideConfirmPassword;
  }

  // Custom Validators
  private passwordValidator(
    control: AbstractControl,
  ): { [key: string]: any } | null {
    const value = control.value;
    if (!value) return null;

    const hasUpperCase = /[A-Z]/.test(value);
    const hasLowerCase = /[a-z]/.test(value);
    const hasNumeric = /[0-9]/.test(value);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(value);

    const valid = hasUpperCase && hasLowerCase && hasNumeric && hasSpecialChar;

    if (!valid) {
      return {
        passwordStrength: {
          hasUpperCase,
          hasLowerCase,
          hasNumeric,
          hasSpecialChar,
        },
      };
    }

    return null;
  }

  private passwordMatchValidator(
    group: AbstractControl,
  ): { [key: string]: any } | null {
    const password = group.get('password');
    const confirmPassword = group.get('confirmPassword');

    if (!password || !confirmPassword) return null;

    return password.value === confirmPassword.value
      ? null
      : { passwordMismatch: true };
  }

  private markFormGroupTouched(): void {
    Object.keys(this.signupForm.controls).forEach((key) => {
      const control = this.signupForm.get(key);
      control?.markAsTouched();
    });
  }

  private showError(message: string): void {
    this.notificationService.showError(message);
  }

  private showSuccess(message: string): void {
    this.notificationService.showSuccess(message);
  }

  // Getter methods for template
  get fullNameControl() {
    return this.signupForm.get('fullName');
  }
  get emailControl() {
    return this.signupForm.get('email');
  }
  get passwordControl() {
    return this.signupForm.get('password');
  }
  get confirmPasswordControl() {
    return this.signupForm.get('confirmPassword');
  }
  get phoneNumberControl() {
    return this.signupForm.get('phoneNumber');
  }
}
