<div class="user-management-container">
  <!-- Table Section with Integrated Header and Filters -->
  <div class="table-section">
    <mat-card class="table-card">
      <!-- Integrated Header Section -->
      <div class="integrated-header">
        <div class="header-content">
          <h1 class="page-title">User List</h1>
          <p class="page-subtitle">
            Manage your team members and their account permissions here.
          </p>
        </div>
      </div>

      <!-- Table Header with Filters -->
      <div class="table-header">
        <div class="table-header-left">
          <div class="filters-container">
            <!-- Search Input -->
            <div class="ant-search-field">
              <label class="ant-filter-label">Search User</label>
              <div class="ant-input-search-wrapper">
                <input
                  type="text"
                  class="ant-input-search"
                  [formControl]="searchControl"
                  placeholder="Search users..."
                />
                <span class="ant-input-search-button">
                  <mat-icon class="ant-search-icon">search</mat-icon>
                </span>
              </div>
            </div>

            <!-- Role Filter -->
            <div class="ant-filter-field">
              <label class="ant-filter-label">Role</label>
              <div class="ant-select-wrapper">
                <select class="ant-select" [formControl]="roleFilter">
                  <option value="">All Roles</option>
                  <option value="admin">Admin</option>
                  <option value="user">User</option>
                  <option value="super admin">Super Admin</option>
                </select>
                <span class="ant-select-arrow">
                  <mat-icon>keyboard_arrow_down</mat-icon>
                </span>
              </div>
            </div>

            <!-- Status Filter -->
            <div class="ant-filter-field">
              <label class="ant-filter-label">Status</label>
              <div class="ant-select-wrapper">
                <select class="ant-select" [formControl]="statusFilter">
                  <option value="all">All</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
                <span class="ant-select-arrow">
                  <mat-icon>keyboard_arrow_down</mat-icon>
                </span>
              </div>
            </div>

            <!-- Reset Button -->
            <!-- <button
              type="button"
              class="ant-btn ant-btn-default reset-btn"
              (click)="resetFilters()"
              title="Reset filters"
            >
              Reset
            </button> -->
          </div>
        </div>

        <div class="table-header-right">
          <div class="header-actions">
            <button
              mat-raised-button
              color="primary"
              class="add-user-btn"
              (click)="onAddUser()"
              *ngIf="hasAdminAccess"
            >
              <mat-icon>person_add</mat-icon>
              Add User
            </button>
          </div>
        </div>
      </div>

      <!-- Loading Indicator -->
      <div *ngIf="loadingStates.fetchingUsers" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
        <p class="loading-text">Loading users...</p>
      </div>

      <!-- Table Content -->
      <div *ngIf="!loadingStates.fetchingUsers" class="table-container">
        <table
          mat-table
          [dataSource]="dataSource"
          matSort
          (matSortChange)="onSortChange($event)"
          class="users-table"
        >
          <!-- Full Name Column -->
          <ng-container matColumnDef="fullName">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Full Name</th>
            <td mat-cell *matCellDef="let user">
              <div class="user-info">
                <!-- Profile pictures removed for minimalist design -->
                <div class="user-avatar-placeholder">
                  <span>{{ user.fullName?.charAt(0) || "U" }}</span>
                </div>
                <div class="user-details">
                  <span class="user-name">{{ user.fullName || "N/A" }}</span>
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Email Column -->
          <ng-container matColumnDef="email">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
            <td mat-cell *matCellDef="let user">
              <span class="user-email">{{ user.email || "N/A" }}</span>
            </td>
          </ng-container>

          <!-- Roles Column -->
          <ng-container matColumnDef="roles">
            <th mat-header-cell *matHeaderCellDef>Roles</th>
            <td mat-cell *matCellDef="let user">
              <div class="roles-container">
                <mat-chip-set>
                  <mat-chip
                    *ngFor="let role of user.roles"
                    [class]="
                      'role-chip role-' + role.toLowerCase().replace(' ', '-')
                    "
                  >
                    {{ role }}
                  </mat-chip>
                </mat-chip-set>
              </div>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="isActive">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>Status</th>
            <td mat-cell *matCellDef="let user">
              <mat-chip
                [class]="user.isActive ? 'status-active' : 'status-inactive'"
              >
                <mat-icon>{{
                  user.isActive ? "check_circle" : "cancel"
                }}</mat-icon>
                {{ user.statusDisplay }}
              </mat-chip>
            </td>
          </ng-container>

          <!-- Created On Column -->
          <ng-container matColumnDef="createdOn">
            <th mat-header-cell *matHeaderCellDef mat-sort-header>
              Created On
            </th>
            <td mat-cell *matCellDef="let user">
              <span class="created-date">{{
                user.formattedCreatedOn | date: "shortDate"
              }}</span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let user">
              <div class="actions-container">
                <button
                  mat-icon-button
                  *ngIf="user.actions.canView"
                  (click)="onViewUser(user)"
                  matTooltip="View Details"
                  class="action-btn view-btn"
                >
                  <mat-icon>visibility</mat-icon>
                </button>
                <button
                  mat-icon-button
                  *ngIf="user.actions.canEdit"
                  (click)="onEditUser(user)"
                  matTooltip="Edit User"
                  class="action-btn edit-btn"
                >
                  <mat-icon>edit</mat-icon>
                </button>
                <button
                  mat-icon-button
                  *ngIf="user.actions.canDelete"
                  (click)="onDeleteUser(user)"
                  matTooltip="Delete User"
                  class="action-btn delete-btn"
                >
                  <mat-icon>delete</mat-icon>
                </button>
              </div>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns"></tr>
        </table>

        <!-- Empty State -->
        <div *ngIf="dataSource.data.length === 0" class="empty-state">
          <mat-icon class="empty-icon">people_outline</mat-icon>
          <h3>No users found</h3>
          <p>No users match your current filters.</p>
        </div>
      </div>

      <!-- Pagination -->
      <mat-paginator
        *ngIf="!loadingStates.fetchingUsers && dataSource.data.length > 0"
        [length]="totalItems"
        [pageSize]="pageSize"
        [pageSizeOptions]="pageSizeOptions"
        [pageIndex]="pageIndex"
        (page)="onPageChange($event)"
        showFirstLastButtons
        class="mat-mdc-paginator"
      ></mat-paginator>
    </mat-card>
  </div>
</div>
