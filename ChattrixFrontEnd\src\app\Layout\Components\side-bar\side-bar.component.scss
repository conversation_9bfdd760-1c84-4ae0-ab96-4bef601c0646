/* Sidebar Container */
.sidebar-container {
  width: 280px;
  height: 100vh;
  background: var(--bg-secondary);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Fix scrollbar issue */
  padding: var(--spacing-md);
  box-sizing: border-box;
  position: relative;
  transition: width var(--transition-normal);

  &.collapsed {
    width: 70px;
    padding: var(--spacing-sm);
  }
}

/* Sidebar Toggle Button */
.sidebar-toggle {
  background: transparent;
  border: none;
  color: var(--text-primary);
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: var(--bg-hover);
  }

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }
}

/* Expand Toggle Button (when collapsed) */
.expand-toggle {
  background: transparent;
  border: none;
  color: var(--text-primary);
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;

  &:hover {
    background: var(--bg-hover);
  }

  mat-icon {
    font-size: 20px;
    width: 20px;
    height: 20px;
  }
}

/* Header Section */
.header-section {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-sm) 0;
  transition: all var(--transition-normal);

  .collapsed & {
    justify-content: center;
    margin-bottom: var(--spacing-md);
  }
}

/* App Branding */
.app-branding {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
}

.app-logo {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.logo-fallback {
  width: 40px;
  height: 40px;
  background: #000000; /* Black background for minimalist design */
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 18px;
}

.app-name {
  color: var(--text-primary);
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0;
  transition: opacity var(--transition-normal);
  white-space: nowrap;
  overflow: hidden;
}

/* Profile Section - Theme Responsive */
.profile-section {
  margin-top: auto;
  transition: all var(--transition-normal);
}

.profile-content {
  padding: var(--spacing-md);
  background: var(--bg-card); /* Theme-responsive background */
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: space-between; /* Space between profile info and logout button */
  gap: var(--spacing-sm);
  transition: all var(--transition-normal);
  margin: var(--spacing-sm);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .collapsed & {
    flex-direction: column;
    padding: var(--spacing-sm);
    gap: var(--spacing-xs);
  }
}

.profile-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  flex: 1;
  min-width: 0;

  .collapsed & {
    margin-bottom: var(--spacing-sm);
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}

.profile-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--bg-tertiary);
  border: 2px solid var(--border-primary);
  overflow: hidden;
  flex-shrink: 0;

  .avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .avatar-initials {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
  }

  // .collapsed & {
  //   width: 32px;
  //   height: 32px;

  //   .avatar-initials {
  //     font-size: 0.85rem;
  //   }
  // }
}

.profile-details {
  min-width: 0;
  flex: 1;
}

.profile-name {
  color: var(--text-primary); /* Theme-responsive text color */
  font-size: 0.95rem;
  font-weight: 600;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.profile-role {
  color: var(--text-secondary); /* Theme-responsive secondary text color */
  font-size: 0.8rem;
  margin: 2px 0 0 0;
  font-weight: 500;
}

/* Navigation Section */
.navigation-section {
  flex: 1;
  margin-bottom: var(--spacing-lg);
  transition: all var(--transition-normal);
  overflow-y: auto; /* Allow scrolling if needed */

  .collapsed & {
    margin-bottom: var(--spacing-md);
  }
}

.nav-list {
  padding: 0;
}

.nav-item {
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-xs);
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-secondary);
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  position: relative; /* For border positioning */
  border-left: 3px solid transparent; /* Initial border state */

  .collapsed & {
    justify-content: center;
    padding: 0 var(--spacing-sm);
    border-left: none; /* Remove border in collapsed state */
  }

  &:hover:not(.active) {
    background: var(--bg-hover);
    color: var(--text-primary);
  }

  &.active {
    background: var(
      --bg-hover
    ) !important; /* Ensure override of Material UI styles */
    color: var(--text-primary) !important;
    border-left: 3px solid var(--accent-primary) !important; /* Subtle accent border */

    .collapsed & {
      border-left: none !important; /* Remove border in collapsed state */
      background: var(--bg-hover) !important;
    }

    .nav-icon {
      color: var(--text-primary) !important;
    }

    .nav-label {
      color: var(--text-primary) !important;
      font-weight: 600 !important; /* Slightly bolder for active state */
    }
  }
}

.nav-icon {
  color: var(--text-secondary);
  margin-right: var(--spacing-sm);
  transition: color var(--transition-fast);
  flex-shrink: 0;

  .collapsed & {
    margin-right: 0;
  }
}

.nav-label {
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.95rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: opacity var(--transition-normal);
}

.nav-badge {
  background: var(--accent-primary); /* Use theme variable for consistency */
  color: var(--accent-secondary);
  border-radius: 12px;
  padding: 2px 8px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
  margin-left: auto;
}

/* Settings Section */
.settings-section {
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--border-primary);
}

.setting-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  min-height: 48px;

  .collapsed & {
    justify-content: center;
    padding: var(--spacing-sm);
    gap: 0;
  }

  &:hover {
    background: var(--bg-hover);
  }
}

.setting-icon {
  color: var(--text-secondary);
  font-size: 20px;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);

  &:hover {
    background: var(--bg-hover);
  }
}

.setting-label {
  flex: 1;
  color: var(--text-primary);
  font-weight: 500;
  font-size: 0.95rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: background-color var(--transition-fast);

  &:hover {
    background: var(--bg-hover);
  }
}

.theme-toggle {
  margin-left: auto;

  ::ng-deep .mat-slide-toggle-bar {
    background-color: var(--bg-tertiary);
  }

  ::ng-deep .mat-slide-toggle-thumb {
    background-color: var(--text-secondary);
  }

  &.mat-checked {
    ::ng-deep .mat-slide-toggle-bar {
      background-color: var(--accent-primary);
    }

    ::ng-deep .mat-slide-toggle-thumb {
      background-color: var(--accent-secondary);
    }
  }
}

.logout-button {
  background: transparent;
  border: 1px solid var(--error);
  color: var(--error);
  border-radius: var(--radius-md);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  flex-shrink: 0;

  &:hover {
    background: var(--error);
    color: white;
    transform: scale(1.05);
  }

  mat-icon {
    font-size: 18px;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  // .collapsed & {
  //   width: 28px;
  //   height: 28px;

  //   mat-icon {
  //     font-size: 14px;
  //     width: 14px;
  //     height: 14px;
  //   }
  // }
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar-container {
    width: 100%;
    height: auto;
    min-height: 100vh;

    &.collapsed {
      width: 100%;
    }
  }

  .sidebar-toggle {
    display: none; /* Hide toggle on mobile */
  }

  .profile-section {
    border-top: 1px solid var(--border-primary);
    flex-direction: row;
    padding: var(--spacing-md);
  }

  .profile-info {
    flex-direction: row;
  }

  .profile-avatar {
    width: 35px;
    height: 35px;
  }

  .app-name {
    font-size: 1.25rem;
  }
}

/* Light Theme Overrides */
:host-context(.light-theme) {
  .sidebar-container {
    background: #ffffff;
    border-right-color: #e0e0e0;
  }

  .sidebar-toggle {
    background: #ffffff;
    border-color: #e0e0e0;
    color: #333333;

    &:hover {
      background: #f5f5f5;
    }
  }

  .nav-item {
    color: #666666;

    &:hover {
      background: #f5f5f5;
      color: #333333;
    }

    &.active {
      background: var(
        --bg-hover
      ) !important; /* Subtle highlight for light theme too */
      color: var(--text-primary) !important;
      border-left: 3px solid var(--accent-primary) !important;

      .collapsed & {
        border-left: none !important;
      }

      .nav-icon {
        color: var(--text-primary) !important;
      }

      .nav-label {
        color: var(--text-primary) !important;
        font-weight: 600 !important;
      }
    }

    .nav-icon {
      color: #666666;
    }
  }

  .setting-item {
    &:hover {
      background: #f5f5f5;
    }
  }

  .setting-icon {
    color: #666666;
  }

  .setting-label {
    color: #333333;
  }

  .app-name {
    color: #333333;
  }
}
