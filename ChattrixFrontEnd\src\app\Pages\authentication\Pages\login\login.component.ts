import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { AuthenticationService } from '../../Services/Authentication.service';
import { AuthStateService } from '../../Services/AuthState.service';
import { NotificationService } from '../../../../Core/Services/notification.service';
import {
  LoginRequest,
  AuthState,
  AuthError,
  UserInfo,
  AuthErrorType,
} from '../../Models';

@Component({
  selector: 'app-login',
  standalone: false,
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss',
})
export class LoginComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  loginForm: FormGroup;
  hidePassword = true;
  logoLoaded = true; // Assume logo loads successfully by default

  // Authentication state
  isAuthenticated = false;
  isLoading = false;
  user: UserInfo | null = null;
  error: string | null = null;

  constructor(
    private authService: AuthenticationService,
    private authState: AuthStateService,
    private formBuilder: FormBuilder,
    private router: Router,
    private notificationService: NotificationService,
  ) {
    this.loginForm = this.formBuilder.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
    });
  }

  ngOnInit(): void {
    // Subscribe to authentication state
    this.authState.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe((state: AuthState) => {
        this.isAuthenticated = state.isAuthenticated;
        this.isLoading = state.isLoading;
        this.user = state.user;
        this.error = state.error;

        // Show error messages
        if (state.error) {
          this.handleAuthError(state.error);
        }

        // Redirect if authenticated
        if (state.isAuthenticated && state.user) {
          this.router.navigate(['/dashboard']);
        }
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onLogin(): void {
    // Clear any previous errors
    this.authState.clearError();

    // Validate form first
    if (!this.loginForm.valid) {
      this.handleFormValidation();
      return;
    }

    const loginData: LoginRequest = this.loginForm.value;

    this.authService.login(loginData).subscribe({
      next: (response) => {
        if (response.data?.requiresOtp || response.data?.RequiresOtp) {
          // Handle 2FA requirement
          this.notificationService.showInfo(
            'Please enter the verification code sent to your device.',
          );
          this.router.navigate(['/auth/otp-verification'], {
            queryParams: {
              userId: response.data.userId || response.data.UserId,
            },
          });
        } else if (response.isSuccess) {
          // Login successful, will be handled by state subscription
          this.notificationService.showSuccess(
            'Login successful! Welcome back.',
          );
          this.router.navigate(['/dashboard']);
        }
      },
      error: (error: AuthError) => {
        // Error is automatically handled by the service and reflected in state
        console.error('Login failed:', error);
        this.handleAuthError(error.message, error.type);
      },
    });
  }

  onForgotPassword(): void {
    // Navigate to forgot password page
    this.router.navigate(['/auth/forgot-password']);
  }

  onSignUp(): void {
    // Navigate to sign up page
    this.router.navigate(['/auth/signup']);
  }

  togglePasswordVisibility(): void {
    this.hidePassword = !this.hidePassword;
  }

  onImageError(event: any): void {
    this.logoLoaded = false;
    console.log('Logo failed to load, showing fallback');
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach((key) => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }

  /**
   * Handles form validation errors
   */
  private handleFormValidation(): void {
    this.markFormGroupTouched();

    const emailControl = this.loginForm.get('email');
    const passwordControl = this.loginForm.get('password');

    if (emailControl?.hasError('required')) {
      this.notificationService.showError('Email address is required.');
      return;
    }

    if (emailControl?.hasError('email')) {
      this.notificationService.showError('Please enter a valid email address.');
      return;
    }

    if (passwordControl?.hasError('required')) {
      this.notificationService.showError('Password is required.');
      return;
    }

    if (passwordControl?.hasError('minlength')) {
      this.notificationService.showError(
        'Password must be at least 6 characters long.',
      );
      return;
    }

    this.notificationService.showError(
      'Please check your input and try again.',
    );
  }

  /**
   * Handles authentication errors with specific messages
   */
  private handleAuthError(message: string, errorType?: AuthErrorType): void {
    switch (errorType) {
      case AuthErrorType.INVALID_CREDENTIALS:
        this.notificationService.showError(
          'Invalid email or password. Please check your credentials and try again.',
        );
        break;
      case AuthErrorType.USER_NOT_FOUND:
        this.notificationService.showError(
          'No account found with this email address. Please check your email or sign up.',
        );
        break;
      case AuthErrorType.EMAIL_NOT_FOUND:
        this.notificationService.showError(
          'Email address not found. Please check your email and try again.',
        );
        break;
      case AuthErrorType.ACCOUNT_LOCKED:
        this.notificationService.showError(
          'Your account has been locked due to multiple failed login attempts. Please try again later or contact support.',
        );
        break;
      case AuthErrorType.ACCOUNT_DISABLED:
        this.notificationService.showError(
          'Your account has been disabled. Please contact support for assistance.',
        );
        break;
      case AuthErrorType.ACCOUNT_INACTIVE:
        this.notificationService.showError(
          'Your account is inactive. Please contact support to activate your account.',
        );
        break;
      case AuthErrorType.NETWORK_ERROR:
        this.notificationService.showError(
          'Network connection error. Please check your internet connection and try again.',
        );
        break;
      case AuthErrorType.SERVER_ERROR:
        this.notificationService.showError(
          'Server error occurred. Please try again later.',
        );
        break;
      case AuthErrorType.VALIDATION_ERROR:
        this.notificationService.showError(
          message || 'Please check your input and try again.',
        );
        break;
      default:
        this.notificationService.showError(
          message || 'An unexpected error occurred. Please try again.',
        );
        break;
    }
  }

  // Getter methods for template
  get emailControl() {
    return this.loginForm.get('email');
  }
  get passwordControl() {
    return this.loginForm.get('password');
  }
}
