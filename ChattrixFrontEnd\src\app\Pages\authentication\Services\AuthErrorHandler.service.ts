import { Injectable } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { throwError } from 'rxjs';
import {
  AuthError,
  AuthErrorType,
  ApiErrorResponse,
  ValidationError,
} from '../Models';

@Injectable({
  providedIn: 'root',
})
export class AuthErrorHandlerService {
  /**
   * Handles HTTP errors and converts them to structured AuthError objects
   */
  handleError(error: HttpErrorResponse): AuthError {
    const apiError: ApiErrorResponse = error.error;

    // Network or client-side error
    if (error.error instanceof ErrorEvent) {
      return {
        type: AuthErrorType.NETWORK_ERROR,
        message:
          'Network error occurred. Please check your connection and try again.',
        details: error.error.message,
        statusCode: 0,
      };
    }

    // Server-side error
    switch (error.status) {
      case 400:
        return this.handleBadRequest(apiError, error.status);
      case 401:
        return this.handleUnauthorized(apiError, error.status);
      case 403:
        return this.handleForbidden(apiError, error.status);
      case 404:
        return this.handleNotFound(apiError, error.status);
      case 500:
      case 502:
      case 503:
      case 504:
        return this.handleServerError(apiError, error.status);
      default:
        return {
          type: AuthErrorType.UNKNOWN_ERROR,
          message: 'An unexpected error occurred. Please try again later.',
          details: apiError?.message,
          statusCode: error.status,
        };
    }
  }

  /**
   * Handles 400 Bad Request errors
   */
  private handleBadRequest(
    apiError: ApiErrorResponse,
    statusCode: number,
  ): AuthError {
    if (apiError?.message) {
      const message = apiError.message.toLowerCase();

      // Check for specific error types
      if (message.includes('otp') || message.includes('verification code')) {
        if (message.includes('expired')) {
          return {
            type: AuthErrorType.OTP_EXPIRED,
            message: 'Verification code has expired. Please request a new one.',
            details: apiError.message,
            statusCode,
          };
        }
        return {
          type: AuthErrorType.OTP_INVALID,
          message: 'Invalid verification code. Please try again.',
          details: apiError.message,
          statusCode,
        };
      }

      if (message.includes('reset token') || message.includes('token')) {
        if (message.includes('expired')) {
          return {
            type: AuthErrorType.RESET_TOKEN_EXPIRED,
            message:
              'Reset token has expired. Please request a new password reset.',
            details: apiError.message,
            statusCode,
          };
        }
        return {
          type: AuthErrorType.RESET_TOKEN_INVALID,
          message: 'Invalid reset token. Please request a new password reset.',
          details: apiError.message,
          statusCode,
        };
      }

      if (message.includes('inactive')) {
        return {
          type: AuthErrorType.ACCOUNT_INACTIVE,
          message: 'Your account is inactive. Please contact support.',
          details: apiError.message,
          statusCode,
        };
      }

      if (message.includes('locked')) {
        return {
          type: AuthErrorType.ACCOUNT_LOCKED,
          message:
            'Your account has been locked due to multiple failed login attempts. Please try again later or contact support.',
          details: apiError.message,
          statusCode,
        };
      }

      if (message.includes('disabled')) {
        return {
          type: AuthErrorType.ACCOUNT_DISABLED,
          message:
            'Your account has been disabled. Please contact support for assistance.',
          details: apiError.message,
          statusCode,
        };
      }

      if (message.includes('email') && message.includes('not found')) {
        return {
          type: AuthErrorType.EMAIL_NOT_FOUND,
          message:
            'Email address not found. Please check your email and try again.',
          details: apiError.message,
          statusCode,
        };
      }

      return {
        type: AuthErrorType.VALIDATION_ERROR,
        message: apiError.message,
        statusCode,
      };
    }

    // Handle validation errors
    if (apiError?.errors) {
      const validationMessage = this.formatValidationErrors(apiError.errors);
      return {
        type: AuthErrorType.VALIDATION_ERROR,
        message: validationMessage,
        details: apiError.errors,
        statusCode,
      };
    }

    return {
      type: AuthErrorType.VALIDATION_ERROR,
      message: 'Please check your input and try again.',
      statusCode,
    };
  }

  /**
   * Handles 401 Unauthorized errors
   */
  private handleUnauthorized(
    apiError: ApiErrorResponse,
    statusCode: number,
  ): AuthError {
    return {
      type: AuthErrorType.INVALID_CREDENTIALS,
      message: 'Invalid email or password. Please try again.',
      details: apiError?.message,
      statusCode,
    };
  }

  /**
   * Handles 403 Forbidden errors
   */
  private handleForbidden(
    apiError: ApiErrorResponse,
    statusCode: number,
  ): AuthError {
    return {
      type: AuthErrorType.UNAUTHORIZED,
      message: 'You do not have permission to perform this action.',
      details: apiError?.message,
      statusCode,
    };
  }

  /**
   * Handles 404 Not Found errors
   */
  private handleNotFound(
    apiError: ApiErrorResponse,
    statusCode: number,
  ): AuthError {
    if (apiError?.message) {
      const message = apiError.message.toLowerCase();

      if (message.includes('email') || message.includes('user')) {
        return {
          type: AuthErrorType.EMAIL_NOT_FOUND,
          message:
            'Email address not found. Please check your email and try again.',
          details: apiError.message,
          statusCode,
        };
      }
    }

    return {
      type: AuthErrorType.USER_NOT_FOUND,
      message: 'User not found. Please check your credentials.',
      details: apiError?.message,
      statusCode,
    };
  }

  /**
   * Handles server errors (5xx)
   */
  private handleServerError(
    apiError: ApiErrorResponse,
    statusCode: number,
  ): AuthError {
    return {
      type: AuthErrorType.SERVER_ERROR,
      message: 'Server error occurred. Please try again later.',
      details: apiError?.message,
      statusCode,
    };
  }

  /**
   * Formats validation errors into user-friendly messages
   */
  private formatValidationErrors(
    errors: { [key: string]: string[] } | ValidationError[],
  ): string {
    if (Array.isArray(errors)) {
      // Handle array of ValidationError objects
      const errorMessages = errors.map(
        (err: any) => err.description || err.message || 'Validation error',
      );
      return `Please fix the following issues: ${errorMessages.join('; ')}`;
    } else {
      // Handle object with field-specific errors
      const errorItems = Object.entries(errors).map(
        ([field, messages]) =>
          `${this.formatFieldName(field)}: ${messages.join(', ')}`,
      );
      return `Please fix the following issues: ${errorItems.join('; ')}`;
    }
  }

  /**
   * Formats field names to be more user-friendly
   */
  private formatFieldName(fieldName: string): string {
    return fieldName
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, (str) => str.toUpperCase())
      .trim();
  }

  /**
   * Creates an RxJS error observable with the structured error
   */
  createErrorObservable(error: HttpErrorResponse) {
    const authError = this.handleError(error);
    return throwError(() => authError);
  }
}
